/**
 * 认证管理模块
 * 处理用户登录状态、权限控制等
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.userRole = 'guest'; // guest, user, admin
        this.loginCallbacks = [];
        this.logoutCallbacks = [];
        
        // 从本地存储恢复用户状态
        this.restoreUserState();
    }
    
    // 从本地存储恢复用户状态
    restoreUserState() {
        const userData = Utils.Storage.get('current_user');
        const userRole = Utils.Storage.get('user_role', 'guest');
        
        if (userData) {
            this.currentUser = userData;
            this.userRole = userRole;
            this.updateUserStatus();
            this.updatePermissions();
        }
    }
    
    // 保存用户状态到本地存储
    saveUserState() {
        if (this.currentUser) {
            Utils.Storage.set('current_user', this.currentUser);
            Utils.Storage.set('user_role', this.userRole);
        } else {
            Utils.Storage.remove('current_user');
            Utils.Storage.remove('user_role');
        }
    }
    
    // 用户登录
    async login(credentials, type = 'user') {
        try {
            let result;
            
            if (type === 'admin') {
                result = await apiService.adminLogin(credentials.phone, credentials.password);
            } else {
                result = await apiService.userLogin(credentials.phone, credentials.password);
            }
            
            if (result.success) {
                this.currentUser = result.data.user || result.data;
                this.userRole = type;
                
                // 设置API token
                if (result.data.token) {
                    apiService.setToken(result.data.token);
                }
                
                this.saveUserState();
                this.updateUserStatus();
                this.updatePermissions();
                
                // 触发登录回调
                this.loginCallbacks.forEach(callback => callback(this.currentUser, this.userRole));
                
                Utils.Toast.success(`${type === 'admin' ? '管理员' : '用户'}登录成功`);
                return { success: true, user: this.currentUser, role: this.userRole };
            } else {
                Utils.Toast.error(result.message || '登录失败');
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('登录错误:', error);
            Utils.Toast.error('登录过程中发生错误');
            return { success: false, message: '登录过程中发生错误' };
        }
    }
    
    // 用户登出
    logout() {
        const wasLoggedIn = this.isLoggedIn();
        
        this.currentUser = null;
        this.userRole = 'guest';
        
        // 清除API token
        apiService.clearToken();
        
        // 清除本地存储
        Utils.Storage.remove('current_user');
        Utils.Storage.remove('user_role');
        Utils.Storage.remove('auth_token');
        
        this.updateUserStatus();
        this.updatePermissions();
        
        if (wasLoggedIn) {
            // 触发登出回调
            this.logoutCallbacks.forEach(callback => callback());
            Utils.Toast.success('已退出登录');
        }
    }
    
    // 检查是否已登录
    isLoggedIn() {
        return this.currentUser !== null;
    }
    
    // 检查是否是管理员
    isAdmin() {
        return this.userRole === 'admin';
    }
    
    // 检查是否是普通用户
    isUser() {
        return this.userRole === 'user';
    }
    
    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }
    
    // 获取用户角色
    getUserRole() {
        return this.userRole;
    }
    
    // 更新用户状态显示
    updateUserStatus() {
        const statusText = Utils.DOM.get('.status-text');
        const loginBtn = Utils.DOM.get('#loginBtn');
        
        if (statusText && loginBtn) {
            if (this.isLoggedIn()) {
                const displayName = this.currentUser.phone || this.currentUser.username || this.currentUser.name || '用户';
                const roleText = this.isAdmin() ? '管理员' : '用户';
                statusText.textContent = `${displayName} (${roleText})`;
                loginBtn.textContent = '退出登录';
                loginBtn.onclick = () => this.logout();
            } else {
                statusText.textContent = '未登录';
                loginBtn.textContent = '登录';
                loginBtn.onclick = () => this.showLoginModal();
            }
        }
    }
    
    // 更新权限控制
    updatePermissions() {
        // 获取所有需要权限控制的元素
        const adminOnlyElements = Utils.DOM.getAll('.admin-only');
        const userOnlyElements = Utils.DOM.getAll('.user-only');
        const guestOnlyElements = Utils.DOM.getAll('.guest-only');
        
        // 管理员专用功能
        adminOnlyElements.forEach(element => {
            if (this.isAdmin()) {
                element.classList.remove('disabled');
                element.style.display = '';
            } else {
                element.classList.add('disabled');
                // 可以选择隐藏或禁用
                // element.style.display = 'none';
            }
        });
        
        // 用户专用功能
        userOnlyElements.forEach(element => {
            if (this.isLoggedIn()) {
                element.classList.remove('disabled');
                element.style.display = '';
            } else {
                element.classList.add('disabled');
            }
        });
        
        // 游客专用功能（未登录时显示）
        guestOnlyElements.forEach(element => {
            if (!this.isLoggedIn()) {
                element.classList.remove('disabled');
                element.style.display = '';
            } else {
                element.classList.add('disabled');
                element.style.display = 'none';
            }
        });
    }
    
    // 显示登录模态框
    showLoginModal() {
        const loginForm = `
            <h3>用户登录</h3>
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="loginPhone">手机号:</label>
                    <input type="tel" id="loginPhone" name="phone" placeholder="请输入手机号" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">密码:</label>
                    <input type="password" id="loginPassword" name="password" placeholder="请输入密码" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">登录</button>
                    <button type="button" class="btn btn-secondary" onclick="authManager.showAdminLoginModal()">管理员登录</button>
                </div>
                <div class="quick-login">
                    <p style="margin: 1rem 0; color: #666; font-size: 0.875rem;">快速测试登录:</p>
                    <button type="button" class="btn btn-info" onclick="authManager.quickLogin('user')" style="margin-right: 0.5rem;">测试用户</button>
                    <button type="button" class="btn btn-info" onclick="authManager.quickLogin('admin')">测试管理员</button>
                </div>
            </form>
            <style>
                .auth-form { max-width: 400px; margin: 0 auto; }
                .form-group { margin-bottom: 1rem; }
                .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
                .form-group input { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; }
                .form-actions { display: flex; gap: 1rem; margin-top: 1.5rem; }
                .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer; font-weight: 500; }
                .btn-primary { background: #667eea; color: white; }
                .btn-secondary { background: #6c757d; color: white; }
                .btn:hover { opacity: 0.9; }
            </style>
        `;
        
        Utils.Modal.show(loginForm);
        
        // 绑定表单提交事件
        setTimeout(() => {
            const form = Utils.DOM.get('#loginForm');
            if (form) {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    const formData = new FormData(form);
                    const credentials = {
                        phone: formData.get('phone'),
                        password: formData.get('password')
                    };
                    
                    const result = await this.login(credentials, 'user');
                    if (result.success) {
                        Utils.Modal.hide();
                    }
                });
            }
        }, 100);
    }
    
    // 显示管理员登录模态框
    showAdminLoginModal() {
        const adminLoginForm = `
            <h3>管理员登录</h3>
            <form id="adminLoginForm" class="auth-form">
                <div class="form-group">
                    <label for="adminPhone">手机号:</label>
                    <input type="tel" id="adminPhone" name="phone" placeholder="请输入管理员手机号" required>
                </div>
                <div class="form-group">
                    <label for="adminPassword">密码:</label>
                    <input type="password" id="adminPassword" name="password" placeholder="请输入管理员密码" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">管理员登录</button>
                    <button type="button" class="btn btn-secondary" onclick="authManager.showLoginModal()">普通用户登录</button>
                </div>
            </form>
        `;
        
        Utils.Modal.show(adminLoginForm);
        
        // 绑定表单提交事件
        setTimeout(() => {
            const form = Utils.DOM.get('#adminLoginForm');
            if (form) {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    const formData = new FormData(form);
                    const credentials = {
                        phone: formData.get('phone'),
                        password: formData.get('password')
                    };
                    
                    const result = await this.login(credentials, 'admin');
                    if (result.success) {
                        Utils.Modal.hide();
                    }
                });
            }
        }, 100);
    }
    
    // 添加登录回调
    onLogin(callback) {
        this.loginCallbacks.push(callback);
    }
    
    // 添加登出回调
    onLogout(callback) {
        this.logoutCallbacks.push(callback);
    }
    
    // 快速登录（使用测试账号）
    async quickLogin(type) {
        let credentials;

        if (type === 'admin') {
            // 使用S1.md中的管理员测试账号
            credentials = {
                phone: '15688515913',
                password: 'admin888'
            };
        } else {
            // 使用默认测试用户账号
            credentials = {
                phone: '13800138000',
                password: 'password123'
            };
        }

        Utils.Toast.info('正在使用测试账号登录...');
        const result = await this.login(credentials, type);

        if (result.success) {
            Utils.Modal.hide();
        }
    }

    // 权限检查装饰器
    requireAuth(callback, requiredRole = 'user') {
        return (...args) => {
            if (!this.isLoggedIn()) {
                Utils.Toast.warning('请先登录');
                this.showLoginModal();
                return;
            }

            if (requiredRole === 'admin' && !this.isAdmin()) {
                Utils.Toast.error('需要管理员权限');
                return;
            }

            return callback.apply(this, args);
        };
    }
}

// 创建全局认证管理器实例
window.authManager = new AuthManager();

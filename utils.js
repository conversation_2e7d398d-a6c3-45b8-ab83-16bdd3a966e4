/**
 * 工具函数库
 * 提供通用的工具方法
 */

// DOM操作工具
const DOM = {
    // 获取元素
    get: (selector) => document.querySelector(selector),
    getAll: (selector) => document.querySelectorAll(selector),
    
    // 创建元素
    create: (tag, className = '', content = '') => {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (content) element.textContent = content;
        return element;
    },
    
    // 显示/隐藏元素
    show: (element) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) element.style.display = 'block';
    },
    
    hide: (element) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) element.style.display = 'none';
    },
    
    // 切换显示状态
    toggle: (element) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) {
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
        }
    }
};

// 消息提示工具
const Toast = {
    show: (message, type = 'info', duration = 3000) => {
        const toast = DOM.get('#toast');
        const messageEl = DOM.get('#toastMessage');
        
        if (toast && messageEl) {
            messageEl.textContent = message;
            toast.className = `toast show ${type}`;
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, duration);
        }
    },
    
    success: (message, duration) => Toast.show(message, 'success', duration),
    error: (message, duration) => Toast.show(message, 'error', duration),
    warning: (message, duration) => Toast.show(message, 'warning', duration)
};

// 模态框工具
const Modal = {
    show: (content, title = '') => {
        const modal = DOM.get('#modal');
        const modalBody = DOM.get('#modalBody');
        
        if (modal && modalBody) {
            if (title) {
                modalBody.innerHTML = `<h3>${title}</h3>${content}`;
            } else {
                modalBody.innerHTML = content;
            }
            modal.style.display = 'block';
        }
    },
    
    hide: () => {
        const modal = DOM.get('#modal');
        if (modal) modal.style.display = 'none';
    }
};

// 本地存储工具
const Storage = {
    set: (key, value) => {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (e) {
            console.error('Storage set error:', e);
            return false;
        }
    },
    
    get: (key, defaultValue = null) => {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            console.error('Storage get error:', e);
            return defaultValue;
        }
    },
    
    remove: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.error('Storage remove error:', e);
            return false;
        }
    },
    
    clear: () => {
        try {
            localStorage.clear();
            return true;
        } catch (e) {
            console.error('Storage clear error:', e);
            return false;
        }
    }
};

// 表单验证工具
const Validator = {
    // 验证手机号
    phone: (phone) => {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },
    
    // 验证邮箱
    email: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // 验证密码强度
    password: (password) => {
        const result = {
            valid: false,
            strength: 'weak',
            messages: []
        };
        
        if (!password) {
            result.messages.push('密码不能为空');
            return result;
        }
        
        if (password.length < 6) {
            result.messages.push('密码长度至少6位');
        }
        
        if (password.length > 20) {
            result.messages.push('密码长度不能超过20位');
        }
        
        if (!/[a-zA-Z]/.test(password)) {
            result.messages.push('密码必须包含字母');
        }
        
        if (!/\d/.test(password)) {
            result.messages.push('密码必须包含数字');
        }
        
        if (result.messages.length === 0) {
            result.valid = true;
            
            // 计算密码强度
            let score = 0;
            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/\d/.test(password)) score++;
            if (/[^a-zA-Z0-9]/.test(password)) score++;
            
            if (score >= 4) result.strength = 'strong';
            else if (score >= 2) result.strength = 'medium';
        }
        
        return result;
    },
    
    // 验证必填字段
    required: (value, fieldName = '字段') => {
        if (!value || value.toString().trim() === '') {
            return { valid: false, message: `${fieldName}不能为空` };
        }
        return { valid: true };
    }
};

// 日期时间工具
const DateTime = {
    // 格式化日期时间
    format: (date, format = 'YYYY-MM-DD HH:mm:ss') => {
        if (!date) return '';
        
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    // 获取当前时间戳
    now: () => Date.now(),
    
    // 获取相对时间描述
    relative: (date) => {
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        
        const minute = 60 * 1000;
        const hour = 60 * minute;
        const day = 24 * hour;
        
        if (diff < minute) return '刚刚';
        if (diff < hour) return `${Math.floor(diff / minute)}分钟前`;
        if (diff < day) return `${Math.floor(diff / hour)}小时前`;
        if (diff < 7 * day) return `${Math.floor(diff / day)}天前`;
        
        return DateTime.format(date, 'YYYY-MM-DD');
    }
};

// 网络请求工具
const Http = {
    // 基础请求方法
    request: async (url, options = {}) => {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            return {
                success: response.ok,
                status: response.status,
                data: data,
                message: data.message || (response.ok ? '请求成功' : '请求失败')
            };
        } catch (error) {
            return {
                success: false,
                status: 0,
                data: null,
                message: error.message || '网络错误'
            };
        }
    },
    
    // GET请求
    get: (url, params = {}) => {
        const urlObj = new URL(url, window.location.origin);
        Object.keys(params).forEach(key => {
            urlObj.searchParams.append(key, params[key]);
        });
        return Http.request(urlObj.toString());
    },
    
    // POST请求
    post: (url, data = {}) => {
        return Http.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    // PUT请求
    put: (url, data = {}) => {
        return Http.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    // DELETE请求
    delete: (url) => {
        return Http.request(url, {
            method: 'DELETE'
        });
    }
};

// 导出工具对象
window.Utils = {
    DOM,
    Toast,
    Modal,
    Storage,
    Validator,
    DateTime,
    Http
};

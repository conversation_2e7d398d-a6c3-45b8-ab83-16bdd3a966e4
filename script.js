/**
 * 主要应用逻辑
 * 处理页面交互、功能路由等
 */

class AppManager {
    constructor() {
        this.currentFunction = null;
        this.functionHandlers = new Map();
        
        // 初始化功能处理器
        this.initFunctionHandlers();
    }
    
    // 初始化功能处理器
    initFunctionHandlers() {
        // 用户功能
        this.functionHandlers.set('login', this.handleLogin.bind(this));
        this.functionHandlers.set('register', this.handleRegister.bind(this));
        this.functionHandlers.set('change-password', this.handleChangePassword.bind(this));
        this.functionHandlers.set('reset-password', this.handleResetPassword.bind(this));
        this.functionHandlers.set('user-apps', this.handleUserApps.bind(this));
        
        // 管理员功能
        this.functionHandlers.set('admin-login', this.handleAdminLogin.bind(this));
        this.functionHandlers.set('user-management', this.handleUserManagement.bind(this));
        this.functionHandlers.set('system-settings', this.handleSystemSettings.bind(this));
        
        // API测试功能
        this.functionHandlers.set('api-test', this.handleApiTest.bind(this));
        this.functionHandlers.set('api-docs', this.handleApiDocs.bind(this));
    }
    
    // 处理功能按钮点击
    handleFunctionClick(functionName) {
        console.log(`点击功能: ${functionName}`);
        
        const handler = this.functionHandlers.get(functionName);
        if (handler) {
            this.currentFunction = functionName;
            handler();
        } else {
            Utils.Toast.warning(`功能 "${functionName}" 暂未实现`);
        }
    }
    
    // 更新内容区域
    updateContentArea(content) {
        const contentArea = Utils.DOM.get('#contentArea');
        if (contentArea) {
            contentArea.innerHTML = content;
        }
    }
    
    // ========== 用户功能处理器 ==========
    
    // 处理登录功能
    handleLogin() {
        if (authManager.isLoggedIn()) {
            Utils.Toast.info('您已经登录了');
            return;
        }
        authManager.showLoginModal();
    }
    
    // 处理注册功能
    handleRegister() {
        const registerForm = `
            <div class="function-content">
                <h2>用户注册</h2>
                <form id="registerForm" class="content-form">
                    <div class="form-group">
                        <label for="regPhone">手机号:</label>
                        <input type="tel" id="regPhone" name="phone" placeholder="请输入11位手机号" required>
                    </div>
                    <div class="form-group">
                        <label for="regPassword">密码:</label>
                        <input type="password" id="regPassword" name="password" placeholder="6-20位密码" required>
                        <div class="password-strength" id="passwordStrength"></div>
                    </div>
                    <div class="form-group">
                        <label for="regCode">验证码:</label>
                        <div class="code-input">
                            <input type="text" id="regCode" name="code" placeholder="请输入验证码" required>
                            <button type="button" id="sendCodeBtn" class="btn btn-secondary">发送验证码</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inviteCode">邀请码:</label>
                        <input type="text" id="inviteCode" name="invite_code" placeholder="请输入邀请码" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">注册</button>
                        <button type="button" class="btn btn-secondary" onclick="appManager.showWelcome()">取消</button>
                    </div>
                </form>
            </div>
            <style>
                .content-form { max-width: 500px; margin: 2rem auto; }
                .form-group { margin-bottom: 1.5rem; }
                .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
                .form-group input { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; }
                .code-input { display: flex; gap: 1rem; }
                .code-input input { flex: 1; }
                .password-strength { margin-top: 0.5rem; font-size: 0.875rem; }
                .password-strength.weak { color: #dc3545; }
                .password-strength.medium { color: #ffc107; }
                .password-strength.strong { color: #28a745; }
                .form-actions { display: flex; gap: 1rem; justify-content: center; margin-top: 2rem; }
                .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer; font-weight: 500; }
                .btn-primary { background: #667eea; color: white; }
                .btn-secondary { background: #6c757d; color: white; }
                .btn:hover { opacity: 0.9; }
                .btn:disabled { opacity: 0.6; cursor: not-allowed; }
            </style>
        `;
        
        this.updateContentArea(registerForm);
        
        // 绑定事件
        setTimeout(() => {
            this.bindRegisterEvents();
        }, 100);
    }
    
    // 绑定注册表单事件
    bindRegisterEvents() {
        const form = Utils.DOM.get('#registerForm');
        const passwordInput = Utils.DOM.get('#regPassword');
        const sendCodeBtn = Utils.DOM.get('#sendCodeBtn');
        
        // 密码强度检查
        if (passwordInput) {
            passwordInput.addEventListener('input', () => {
                const password = passwordInput.value;
                const strengthEl = Utils.DOM.get('#passwordStrength');
                
                if (password && strengthEl) {
                    const validation = Utils.Validator.password(password);
                    strengthEl.textContent = validation.valid ? 
                        `密码强度: ${validation.strength}` : 
                        validation.messages.join(', ');
                    strengthEl.className = `password-strength ${validation.strength}`;
                }
            });
        }
        
        // 发送验证码
        if (sendCodeBtn) {
            sendCodeBtn.addEventListener('click', async () => {
                const phoneInput = Utils.DOM.get('#regPhone');
                const phone = phoneInput?.value;
                
                if (!phone || !Utils.Validator.phone(phone)) {
                    Utils.Toast.error('请输入正确的手机号');
                    return;
                }
                
                sendCodeBtn.disabled = true;
                sendCodeBtn.textContent = '发送中...';
                
                try {
                    const result = await apiService.sendRegisterCode(phone);
                    if (result.success) {
                        Utils.Toast.success('验证码发送成功');
                        this.startCountdown(sendCodeBtn, 60);
                    } else {
                        Utils.Toast.error(result.message || '发送失败');
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '发送验证码';
                    }
                } catch (error) {
                    Utils.Toast.error('发送验证码失败');
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                }
            });
        }
        
        // 表单提交
        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(form);
                const data = {
                    phone: formData.get('phone'),
                    password: formData.get('password'),
                    code: formData.get('code'),
                    invite_code: formData.get('invite_code')
                };
                
                // 验证数据
                if (!Utils.Validator.phone(data.phone)) {
                    Utils.Toast.error('请输入正确的手机号');
                    return;
                }
                
                const passwordValidation = Utils.Validator.password(data.password);
                if (!passwordValidation.valid) {
                    Utils.Toast.error(passwordValidation.messages[0]);
                    return;
                }
                
                if (!data.code) {
                    Utils.Toast.error('请输入验证码');
                    return;
                }
                
                if (!data.invite_code) {
                    Utils.Toast.error('请输入邀请码');
                    return;
                }
                
                // 提交注册
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.textContent = '注册中...';
                
                try {
                    const result = await apiService.userRegister(
                        data.phone, 
                        data.password, 
                        data.code, 
                        data.invite_code
                    );
                    
                    if (result.success) {
                        Utils.Toast.success('注册成功！');
                        this.showWelcome();
                    } else {
                        Utils.Toast.error(result.message || '注册失败');
                    }
                } catch (error) {
                    Utils.Toast.error('注册过程中发生错误');
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '注册';
                }
            });
        }
    }
    
    // 倒计时功能
    startCountdown(button, seconds) {
        let count = seconds;
        const timer = setInterval(() => {
            if (count <= 0) {
                clearInterval(timer);
                button.disabled = false;
                button.textContent = '发送验证码';
            } else {
                button.textContent = `${count}秒后重试`;
                count--;
            }
        }, 1000);
    }
    
    // 处理修改密码功能
    handleChangePassword() {
        if (!authManager.isLoggedIn()) {
            Utils.Toast.warning('请先登录');
            authManager.showLoginModal();
            return;
        }
        
        const changePasswordForm = `
            <div class="function-content">
                <h2>修改密码</h2>
                <form id="changePasswordForm" class="content-form">
                    <div class="form-group">
                        <label for="oldPassword">当前密码:</label>
                        <input type="password" id="oldPassword" name="old_password" placeholder="请输入当前密码" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码:</label>
                        <input type="password" id="newPassword" name="new_password" placeholder="请输入新密码" required>
                        <div class="password-strength" id="newPasswordStrength"></div>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认新密码:</label>
                        <input type="password" id="confirmPassword" name="confirm_password" placeholder="请再次输入新密码" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">修改密码</button>
                        <button type="button" class="btn btn-secondary" onclick="appManager.showWelcome()">取消</button>
                    </div>
                </form>
            </div>
        `;
        
        this.updateContentArea(changePasswordForm);
        
        // 绑定事件
        setTimeout(() => {
            this.bindChangePasswordEvents();
        }, 100);
    }
    
    // 绑定修改密码事件
    bindChangePasswordEvents() {
        const form = Utils.DOM.get('#changePasswordForm');
        const newPasswordInput = Utils.DOM.get('#newPassword');
        
        // 新密码强度检查
        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', () => {
                const password = newPasswordInput.value;
                const strengthEl = Utils.DOM.get('#newPasswordStrength');
                
                if (password && strengthEl) {
                    const validation = Utils.Validator.password(password);
                    strengthEl.textContent = validation.valid ? 
                        `密码强度: ${validation.strength}` : 
                        validation.messages.join(', ');
                    strengthEl.className = `password-strength ${validation.strength}`;
                }
            });
        }
        
        // 表单提交
        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(form);
                const oldPassword = formData.get('old_password');
                const newPassword = formData.get('new_password');
                const confirmPassword = formData.get('confirm_password');
                
                // 验证数据
                if (!oldPassword) {
                    Utils.Toast.error('请输入当前密码');
                    return;
                }
                
                const passwordValidation = Utils.Validator.password(newPassword);
                if (!passwordValidation.valid) {
                    Utils.Toast.error(passwordValidation.messages[0]);
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    Utils.Toast.error('两次输入的新密码不一致');
                    return;
                }
                
                if (oldPassword === newPassword) {
                    Utils.Toast.error('新密码不能与当前密码相同');
                    return;
                }
                
                // 提交修改
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.textContent = '修改中...';
                
                try {
                    const currentUser = authManager.getCurrentUser();
                    if (!currentUser || !currentUser.id) {
                        Utils.Toast.error('获取用户信息失败，请重新登录');
                        return;
                    }

                    let result;
                    if (authManager.isAdmin()) {
                        result = await apiService.adminChangePassword(currentUser.id, oldPassword, newPassword);
                    } else {
                        result = await apiService.changePassword(currentUser.id, oldPassword, newPassword);
                    }

                    if (result.success) {
                        Utils.Toast.success('密码修改成功！');
                        this.showWelcome();
                    } else {
                        Utils.Toast.error(result.message || '密码修改失败');
                    }
                } catch (error) {
                    Utils.Toast.error('修改密码过程中发生错误');
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '修改密码';
                }
            });
        }
    }

    // 处理密码重置功能
    handleResetPassword() {
        const resetPasswordForm = `
            <div class="function-content">
                <h2>重置密码</h2>
                <div class="reset-tabs">
                    <button class="tab-btn active" onclick="appManager.switchResetTab('user', this)">用户重置</button>
                    <button class="tab-btn" onclick="appManager.switchResetTab('admin', this)">管理员重置</button>
                </div>
                <form id="resetPasswordForm" class="content-form">
                    <div class="form-group">
                        <label for="resetPhone">手机号:</label>
                        <input type="tel" id="resetPhone" name="phone" placeholder="请输入手机号" required>
                    </div>
                    <div class="form-group">
                        <label for="resetCode">验证码:</label>
                        <div class="code-input">
                            <input type="text" id="resetCode" name="code" placeholder="请输入验证码" required>
                            <button type="button" id="sendResetCodeBtn" class="btn btn-secondary">发送验证码</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="resetNewPassword">新密码:</label>
                        <input type="password" id="resetNewPassword" name="new_password" placeholder="请输入新密码" required>
                        <div class="password-strength" id="resetPasswordStrength"></div>
                    </div>
                    <div class="form-group">
                        <label for="resetConfirmPassword">确认新密码:</label>
                        <input type="password" id="resetConfirmPassword" name="confirm_password" placeholder="请再次输入新密码" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">重置密码</button>
                        <button type="button" class="btn btn-secondary" onclick="appManager.showWelcome()">取消</button>
                    </div>
                </form>
            </div>
            <style>
                .reset-tabs { display: flex; margin-bottom: 2rem; border-bottom: 1px solid #ddd; }
                .tab-btn {
                    padding: 0.75rem 1.5rem; border: none; background: none;
                    cursor: pointer; border-bottom: 2px solid transparent;
                    font-weight: 500; color: #666;
                }
                .tab-btn.active { color: #667eea; border-bottom-color: #667eea; }
                .tab-btn:hover { color: #667eea; }
            </style>
        `;

        this.updateContentArea(resetPasswordForm);
        this.currentResetType = 'user';

        // 绑定事件
        setTimeout(() => {
            this.bindResetPasswordEvents();
        }, 100);
    }

    // 切换重置类型
    switchResetTab(type, element) {
        this.currentResetType = type;

        // 更新标签样式
        const tabs = Utils.DOM.getAll('.tab-btn');
        tabs.forEach(tab => tab.classList.remove('active'));
        if (element) {
            element.classList.add('active');
        }

        // 更新表单标题
        const title = Utils.DOM.get('.function-content h2');
        if (title) {
            title.textContent = type === 'admin' ? '管理员密码重置' : '用户密码重置';
        }
    }

    // 绑定密码重置事件
    bindResetPasswordEvents() {
        const form = Utils.DOM.get('#resetPasswordForm');
        const passwordInput = Utils.DOM.get('#resetNewPassword');
        const sendCodeBtn = Utils.DOM.get('#sendResetCodeBtn');

        // 密码强度检查
        if (passwordInput) {
            passwordInput.addEventListener('input', () => {
                const password = passwordInput.value;
                const strengthEl = Utils.DOM.get('#resetPasswordStrength');

                if (password && strengthEl) {
                    const validation = Utils.Validator.password(password);
                    strengthEl.textContent = validation.valid ?
                        `密码强度: ${validation.strength}` :
                        validation.messages.join(', ');
                    strengthEl.className = `password-strength ${validation.strength}`;
                }
            });
        }

        // 发送验证码
        if (sendCodeBtn) {
            sendCodeBtn.addEventListener('click', async () => {
                const phoneInput = Utils.DOM.get('#resetPhone');
                const phone = phoneInput?.value;

                if (!phone || !Utils.Validator.phone(phone)) {
                    Utils.Toast.error('请输入正确的手机号');
                    return;
                }

                sendCodeBtn.disabled = true;
                sendCodeBtn.textContent = '发送中...';

                try {
                    let result;
                    if (this.currentResetType === 'admin') {
                        result = await apiService.sendAdminForgotPasswordCode(phone);
                    } else {
                        result = await apiService.sendForgotPasswordCode(phone);
                    }

                    if (result.success) {
                        Utils.Toast.success('验证码发送成功');
                        this.startCountdown(sendCodeBtn, 60);
                    } else {
                        Utils.Toast.error(result.message || '发送失败');
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '发送验证码';
                    }
                } catch (error) {
                    Utils.Toast.error('发送验证码失败');
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                }
            });
        }

        // 表单提交
        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();

                const formData = new FormData(form);
                const data = {
                    phone: formData.get('phone'),
                    code: formData.get('code'),
                    new_password: formData.get('new_password'),
                    confirm_password: formData.get('confirm_password')
                };

                // 验证数据
                if (!Utils.Validator.phone(data.phone)) {
                    Utils.Toast.error('请输入正确的手机号');
                    return;
                }

                if (!data.code) {
                    Utils.Toast.error('请输入验证码');
                    return;
                }

                const passwordValidation = Utils.Validator.password(data.new_password);
                if (!passwordValidation.valid) {
                    Utils.Toast.error(passwordValidation.messages[0]);
                    return;
                }

                if (data.new_password !== data.confirm_password) {
                    Utils.Toast.error('两次输入的密码不一致');
                    return;
                }

                // 提交重置
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.textContent = '重置中...';

                try {
                    let result;
                    if (this.currentResetType === 'admin') {
                        result = await apiService.adminResetPassword(data.phone, data.code, data.new_password);
                    } else {
                        result = await apiService.resetPassword(data.phone, data.code, data.new_password);
                    }

                    if (result.success) {
                        Utils.Toast.success('密码重置成功！');
                        this.showWelcome();
                    } else {
                        Utils.Toast.error(result.message || '密码重置失败');
                    }
                } catch (error) {
                    Utils.Toast.error('密码重置过程中发生错误');
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '重置密码';
                }
            });
        }
    }

    // 显示欢迎页面
    showWelcome() {
        const welcomeContent = `
            <div class="welcome-message">
                <h2>欢迎使用API系统管理界面</h2>
                <p>请选择上方的功能按钮开始测试</p>
                <div class="system-info">
                    <h3>系统信息</h3>
                    <ul>
                        <li>当前版本: v1.0.0</li>
                        <li>构建时间: ${Utils.DateTime.format(new Date(), 'YYYY-MM-DD HH:mm:ss')}</li>
                        <li>环境: 开发测试</li>
                        <li>用户状态: ${authManager.isLoggedIn() ? `已登录 (${authManager.getUserRole()})` : '未登录'}</li>
                    </ul>
                </div>
            </div>
        `;
        this.updateContentArea(welcomeContent);
    }
    
    // ========== 应用管理功能 ==========

    handleUserApps() {
        if (!authManager.isLoggedIn()) {
            Utils.Toast.warning('请先登录');
            authManager.showLoginModal();
            return;
        }

        this.showUserAppsInterface();
    }

    // 显示用户应用管理界面
    async showUserAppsInterface() {
        const appsContent = `
            <div class="function-content">
                <div class="apps-header">
                    <h2>我的应用</h2>
                    <button class="btn btn-primary" onclick="appManager.showCreateAppModal()">创建应用</button>
                </div>
                <div class="apps-list" id="appsList">
                    <div class="loading">正在加载应用列表...</div>
                </div>
            </div>
            <style>
                .apps-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; }
                .apps-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 1.5rem; }
                .app-card { background: white; border: 1px solid #e1e5e9; border-radius: 8px; padding: 1.5rem; }
                .app-card h3 { margin-bottom: 0.5rem; color: #333; }
                .app-meta { font-size: 0.875rem; color: #666; margin-bottom: 1rem; }
                .app-keys { margin-bottom: 1rem; }
                .key-item { margin-bottom: 0.5rem; font-family: monospace; font-size: 0.875rem; }
                .key-label { font-weight: 500; color: #555; }
                .key-value { color: #333; word-break: break-all; }
                .app-actions { display: flex; gap: 0.5rem; flex-wrap: wrap; }
                .app-actions .btn { font-size: 0.875rem; padding: 0.5rem 1rem; }
                .loading { text-align: center; padding: 2rem; color: #666; }
                .empty-state { text-align: center; padding: 3rem; color: #666; }
                .empty-state h3 { margin-bottom: 1rem; }
            </style>
        `;

        this.updateContentArea(appsContent);

        // 加载应用列表
        await this.loadUserApps();
    }

    // 加载用户应用列表
    async loadUserApps() {
        const appsList = Utils.DOM.get('#appsList');
        if (!appsList) return;

        try {
            const result = await apiService.getUserApps();

            if (result.success) {
                const apps = result.data || [];
                if (apps.length === 0) {
                    appsList.innerHTML = `
                        <div class="empty-state">
                            <h3>暂无应用</h3>
                            <p>您还没有创建任何应用，点击"创建应用"开始</p>
                            <button class="btn btn-primary" onclick="appManager.showCreateAppModal()">创建应用</button>
                        </div>
                    `;
                } else {
                    appsList.innerHTML = apps.map(app => this.createAppCard(app)).join('');
                }
            } else {
                appsList.innerHTML = `<div class="loading">加载失败: ${result.message}</div>`;
            }
        } catch (error) {
            console.error('加载应用列表失败:', error);
            appsList.innerHTML = `<div class="loading">加载失败，请稍后重试</div>`;
        }
    }

    // 创建应用卡片HTML
    createAppCard(app) {
        const typeMap = { 1: '拍照搜题' };
        const statusMap = { 1: '正常', 2: '冻结' };

        return `
            <div class="app-card">
                <h3>${app.name}</h3>
                <div class="app-meta">
                    类型: ${typeMap[app.type] || '未知'} |
                    状态: ${statusMap[app.status] || '未知'} |
                    创建时间: ${Utils.DateTime.format(app.created_at, 'YYYY-MM-DD')}
                </div>
                <div class="app-keys">
                    <div class="key-item">
                        <div class="key-label">App Key:</div>
                        <div class="key-value">${app.app_key || '未生成'}</div>
                    </div>
                    <div class="key-item">
                        <div class="key-label">Secret Key:</div>
                        <div class="key-value">********************************</div>
                    </div>
                </div>
                <div class="app-actions">
                    <button class="btn btn-primary" onclick="appManager.showAppDetail(${app.id})">查看详情</button>
                    <button class="btn btn-secondary" onclick="appManager.showEditAppModal(${app.id})">编辑</button>
                    <button class="btn btn-warning" onclick="appManager.resetAppSecret(${app.id})">重置密钥</button>
                    ${app.status === 1 ?
                        `<button class="btn btn-danger" onclick="appManager.toggleAppStatus(${app.id}, 2)">冻结</button>` :
                        `<button class="btn btn-success" onclick="appManager.toggleAppStatus(${app.id}, 1)">恢复</button>`
                    }
                </div>
            </div>
        `;
    }

    // 显示创建应用模态框
    showCreateAppModal() {
        const createAppForm = `
            <h3>创建应用</h3>
            <form id="createAppForm" class="modal-form">
                <div class="form-group">
                    <label for="appName">应用名称:</label>
                    <input type="text" id="appName" name="name" placeholder="请输入应用名称" required maxlength="50">
                </div>
                <div class="form-group">
                    <label for="appType">应用类型:</label>
                    <select id="appType" name="type" required>
                        <option value="">请选择应用类型</option>
                        <option value="1">拍照搜题</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">创建应用</button>
                    <button type="button" class="btn btn-secondary" onclick="Utils.Modal.hide()">取消</button>
                </div>
            </form>
            <style>
                .modal-form { max-width: 400px; margin: 0 auto; }
                .modal-form .form-group { margin-bottom: 1.5rem; }
                .modal-form label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
                .modal-form input, .modal-form select { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; }
                .modal-form .form-actions { display: flex; gap: 1rem; justify-content: center; margin-top: 2rem; }
            </style>
        `;

        Utils.Modal.show(createAppForm);

        // 绑定表单提交事件
        setTimeout(() => {
            const form = Utils.DOM.get('#createAppForm');
            if (form) {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleCreateApp(e);
                });
            }
        }, 100);
    }

    // 处理创建应用
    async handleCreateApp(e) {
        const formData = new FormData(e.target);
        const name = formData.get('name').trim();
        const type = parseInt(formData.get('type'));

        if (!name) {
            Utils.Toast.error('请输入应用名称');
            return;
        }

        if (!type) {
            Utils.Toast.error('请选择应用类型');
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '创建中...';

        try {
            const result = await apiService.createApp(name, type);

            if (result.success) {
                Utils.Toast.success('应用创建成功！');
                Utils.Modal.hide();

                // 刷新应用列表
                if (this.currentFunction === 'user-apps') {
                    await this.loadUserApps();
                }
            } else {
                Utils.Toast.error(result.message || '创建应用失败');
            }
        } catch (error) {
            console.error('创建应用失败:', error);
            Utils.Toast.error('创建应用失败，请稍后重试');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '创建应用';
        }
    }

    // 显示编辑应用模态框
    async showEditAppModal(appId) {
        try {
            const result = await apiService.getAppDetail(appId);

            if (result.success) {
                const app = result.data;

                const editAppForm = `
                    <h3>编辑应用</h3>
                    <form id="editAppForm" class="modal-form">
                        <div class="form-group">
                            <label for="editAppName">应用名称:</label>
                            <input type="text" id="editAppName" name="name" value="${app.name}" placeholder="请输入应用名称" required maxlength="50">
                        </div>
                        <div class="form-group">
                            <label for="editAppType">应用类型:</label>
                            <select id="editAppType" name="type" disabled>
                                <option value="1" ${app.type === 1 ? 'selected' : ''}>拍照搜题</option>
                            </select>
                            <small style="color: #666; font-size: 0.875rem;">应用类型创建后不可修改</small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">保存修改</button>
                            <button type="button" class="btn btn-secondary" onclick="Utils.Modal.hide()">取消</button>
                        </div>
                    </form>
                `;

                Utils.Modal.show(editAppForm);

                // 绑定表单提交事件
                setTimeout(() => {
                    const form = Utils.DOM.get('#editAppForm');
                    if (form) {
                        form.addEventListener('submit', async (e) => {
                            e.preventDefault();
                            await this.handleEditApp(e, appId);
                        });
                    }
                }, 100);
            } else {
                Utils.Toast.error(result.message || '获取应用信息失败');
            }
        } catch (error) {
            console.error('获取应用信息失败:', error);
            Utils.Toast.error('获取应用信息失败，请稍后重试');
        }
    }

    // 处理编辑应用
    async handleEditApp(e, appId) {
        const formData = new FormData(e.target);
        const name = formData.get('name').trim();

        if (!name) {
            Utils.Toast.error('请输入应用名称');
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '保存中...';

        try {
            const result = await apiService.updateApp(appId, { name });

            if (result.success) {
                Utils.Toast.success('应用信息更新成功！');
                Utils.Modal.hide();

                // 刷新应用列表
                if (this.currentFunction === 'user-apps') {
                    await this.loadUserApps();
                }
            } else {
                Utils.Toast.error(result.message || '更新应用失败');
            }
        } catch (error) {
            console.error('更新应用失败:', error);
            Utils.Toast.error('更新应用失败，请稍后重试');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '保存修改';
        }
    }
    
    // 显示应用详情
    async showAppDetail(appId) {
        try {
            const result = await apiService.getAppDetail(appId);

            if (result.success) {
                const app = result.data;
                const typeMap = { 1: '拍照搜题' };
                const statusMap = { 1: '正常', 2: '冻结' };

                const detailContent = `
                    <h3>应用详情</h3>
                    <div class="app-detail">
                        <div class="detail-item">
                            <label>应用名称:</label>
                            <span>${app.name}</span>
                        </div>
                        <div class="detail-item">
                            <label>应用类型:</label>
                            <span>${typeMap[app.type] || '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <label>应用状态:</label>
                            <span class="status-${app.status}">${statusMap[app.status] || '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <label>App Key:</label>
                            <div class="key-display">
                                <span class="key-value">${app.app_key || '未生成'}</span>
                                <button class="btn btn-sm" onclick="navigator.clipboard.writeText('${app.app_key}'); Utils.Toast.success('已复制到剪贴板')">复制</button>
                            </div>
                        </div>
                        <div class="detail-item">
                            <label>Secret Key:</label>
                            <div class="key-display">
                                <span class="key-value" id="secretKey">********************************</span>
                                <button class="btn btn-sm" onclick="appManager.toggleSecretKey('${app.secret_key}', this)">显示</button>
                                <button class="btn btn-sm" onclick="navigator.clipboard.writeText('${app.secret_key}'); Utils.Toast.success('已复制到剪贴板')">复制</button>
                            </div>
                        </div>
                        <div class="detail-item">
                            <label>创建时间:</label>
                            <span>${Utils.DateTime.format(app.created_at)}</span>
                        </div>
                        <div class="detail-item">
                            <label>更新时间:</label>
                            <span>${Utils.DateTime.format(app.updated_at)}</span>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="appManager.showEditAppModal(${app.id})">编辑应用</button>
                        <button class="btn btn-warning" onclick="appManager.resetAppSecret(${app.id})">重置密钥</button>
                        <button class="btn btn-primary" onclick="Utils.Modal.hide()">关闭</button>
                    </div>
                    <style>
                        .app-detail { margin: 1.5rem 0; }
                        .detail-item { margin-bottom: 1rem; display: flex; align-items: center; }
                        .detail-item label { min-width: 100px; font-weight: 500; color: #555; }
                        .detail-item span { flex: 1; }
                        .key-display { display: flex; align-items: center; gap: 0.5rem; flex: 1; }
                        .key-value { font-family: monospace; background: #f8f9fa; padding: 0.5rem; border-radius: 4px; flex: 1; }
                        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
                        .status-1 { color: #28a745; font-weight: 500; }
                        .status-2 { color: #dc3545; font-weight: 500; }
                        .modal-actions { display: flex; gap: 1rem; justify-content: center; margin-top: 2rem; }
                    </style>
                `;

                Utils.Modal.show(detailContent);
            } else {
                Utils.Toast.error(result.message || '获取应用详情失败');
            }
        } catch (error) {
            console.error('获取应用详情失败:', error);
            Utils.Toast.error('获取应用详情失败，请稍后重试');
        }
    }

    // 切换密钥显示
    toggleSecretKey(secretKey, buttonElement) {
        const keyElement = Utils.DOM.get('#secretKey');
        const button = buttonElement;

        if (keyElement.textContent === '********************************') {
            keyElement.textContent = secretKey;
            button.textContent = '隐藏';
        } else {
            keyElement.textContent = '********************************';
            button.textContent = '显示';
        }
    }

    // 重置应用密钥
    async resetAppSecret(appId) {
        if (!confirm('确定要重置应用密钥吗？重置后旧密钥将失效。')) {
            return;
        }

        try {
            const result = await apiService.resetAppSecret(appId);

            if (result.success) {
                Utils.Toast.success('密钥重置成功！');

                // 刷新应用列表
                if (this.currentFunction === 'user-apps') {
                    await this.loadUserApps();
                }
            } else {
                Utils.Toast.error(result.message || '重置密钥失败');
            }
        } catch (error) {
            console.error('重置密钥失败:', error);
            Utils.Toast.error('重置密钥失败，请稍后重试');
        }
    }

    // 切换应用状态
    async toggleAppStatus(appId, newStatus) {
        const statusText = newStatus === 1 ? '恢复' : '冻结';

        if (!confirm(`确定要${statusText}此应用吗？`)) {
            return;
        }

        try {
            const result = await apiService.put(`/user/apps/${appId}/status`, { status: newStatus });

            if (result.success) {
                Utils.Toast.success(`应用${statusText}成功！`);

                // 刷新应用列表
                if (this.currentFunction === 'user-apps') {
                    await this.loadUserApps();
                }
            } else {
                Utils.Toast.error(result.message || `${statusText}应用失败`);
            }
        } catch (error) {
            console.error(`${statusText}应用失败:`, error);
            Utils.Toast.error(`${statusText}应用失败，请稍后重试`);
        }
    }

    // ========== 其他功能处理器 ==========

    handleAdminLogin() {
        authManager.showAdminLoginModal();
    }

    handleUserManagement() {
        if (!authManager.isAdmin()) {
            Utils.Toast.warning('需要管理员权限');
            return;
        }
        Utils.Toast.info('用户管理功能开发中...');
    }

    handleSystemSettings() {
        if (!authManager.isAdmin()) {
            Utils.Toast.warning('需要管理员权限');
            return;
        }
        Utils.Toast.info('系统设置功能开发中...');
    }

    handleApiTest() {
        this.showApiTestInterface();
    }

    handleApiDocs() {
        this.showApiDocsInterface();
    }

    // 显示API测试界面
    showApiTestInterface() {
        const apiTestContent = `
            <div class="function-content">
                <h2>API测试工具</h2>
                <div class="api-test-form">
                    <div class="form-group">
                        <label for="apiMethod">请求方法:</label>
                        <select id="apiMethod">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="apiUrl">API地址:</label>
                        <input type="text" id="apiUrl" placeholder="例如: /api/v1/user/info" value="/api/v1/user/info">
                    </div>
                    <div class="form-group">
                        <label for="apiHeaders">请求头 (JSON格式):</label>
                        <textarea id="apiHeaders" rows="3" placeholder='{"Content-Type": "application/json"}'></textarea>
                    </div>
                    <div class="form-group">
                        <label for="apiBody">请求体 (JSON格式):</label>
                        <textarea id="apiBody" rows="5" placeholder='{"key": "value"}'></textarea>
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-primary" onclick="appManager.executeApiTest()">发送请求</button>
                        <button class="btn btn-secondary" onclick="appManager.clearApiTest()">清空</button>
                    </div>
                </div>
                <div class="api-response" id="apiResponse" style="display: none;">
                    <h3>响应结果</h3>
                    <pre id="responseContent"></pre>
                </div>
            </div>
            <style>
                .api-test-form { max-width: 600px; margin: 0 auto; }
                .api-test-form .form-group { margin-bottom: 1.5rem; }
                .api-test-form label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
                .api-test-form input, .api-test-form select, .api-test-form textarea {
                    width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;
                }
                .api-test-form textarea { font-family: monospace; }
                .api-response { margin-top: 2rem; }
                .api-response pre {
                    background: #f8f9fa; padding: 1rem; border-radius: 4px;
                    overflow-x: auto; font-size: 0.875rem;
                }
            </style>
        `;

        this.updateContentArea(apiTestContent);
    }

    // 执行API测试
    async executeApiTest() {
        const method = Utils.DOM.get('#apiMethod').value;
        const url = Utils.DOM.get('#apiUrl').value;
        const headersText = Utils.DOM.get('#apiHeaders').value;
        const bodyText = Utils.DOM.get('#apiBody').value;

        if (!url) {
            Utils.Toast.error('请输入API地址');
            return;
        }

        try {
            // 解析请求头（暂时不使用，为后续扩展预留）
            if (headersText) {
                JSON.parse(headersText); // 验证JSON格式
            }

            let body = null;
            if (bodyText && (method === 'POST' || method === 'PUT')) {
                body = JSON.parse(bodyText);
            }

            const responseDiv = Utils.DOM.get('#apiResponse');
            const responseContent = Utils.DOM.get('#responseContent');

            responseDiv.style.display = 'block';
            responseContent.textContent = '请求中...';

            let result;
            switch (method) {
                case 'GET':
                    result = await apiService.get(url);
                    break;
                case 'POST':
                    result = await apiService.post(url, body);
                    break;
                case 'PUT':
                    result = await apiService.put(url, body);
                    break;
                case 'DELETE':
                    result = await apiService.delete(url);
                    break;
            }

            responseContent.textContent = JSON.stringify(result, null, 2);

        } catch (error) {
            const responseDiv = Utils.DOM.get('#apiResponse');
            const responseContent = Utils.DOM.get('#responseContent');

            responseDiv.style.display = 'block';
            responseContent.textContent = `错误: ${error.message}`;

            Utils.Toast.error('API测试失败: ' + error.message);
        }
    }

    // 清空API测试表单
    clearApiTest() {
        Utils.DOM.get('#apiUrl').value = '';
        Utils.DOM.get('#apiHeaders').value = '';
        Utils.DOM.get('#apiBody').value = '';
        Utils.DOM.get('#apiResponse').style.display = 'none';
    }

    // 显示API文档界面
    showApiDocsInterface() {
        const apiDocsContent = `
            <div class="function-content">
                <h2>API文档</h2>
                <div class="api-docs">
                    <div class="doc-section">
                        <h3>用户认证</h3>
                        <div class="api-item">
                            <div class="api-method post">POST</div>
                            <div class="api-path">/api/v1/user/login</div>
                            <div class="api-desc">用户登录</div>
                        </div>
                        <div class="api-item">
                            <div class="api-method post">POST</div>
                            <div class="api-path">/api/v1/user/register</div>
                            <div class="api-desc">用户注册</div>
                        </div>
                    </div>

                    <div class="doc-section">
                        <h3>应用管理</h3>
                        <div class="api-item">
                            <div class="api-method get">GET</div>
                            <div class="api-path">/api/v1/user/apps</div>
                            <div class="api-desc">获取用户应用列表</div>
                        </div>
                        <div class="api-item">
                            <div class="api-method post">POST</div>
                            <div class="api-path">/api/v1/user/apps</div>
                            <div class="api-desc">创建新应用</div>
                        </div>
                        <div class="api-item">
                            <div class="api-method get">GET</div>
                            <div class="api-path">/api/v1/user/apps/{id}</div>
                            <div class="api-desc">获取应用详情</div>
                        </div>
                        <div class="api-item">
                            <div class="api-method put">PUT</div>
                            <div class="api-path">/api/v1/user/apps/{id}</div>
                            <div class="api-desc">更新应用信息</div>
                        </div>
                    </div>

                    <div class="doc-section">
                        <h3>管理员功能</h3>
                        <div class="api-item">
                            <div class="api-method post">POST</div>
                            <div class="api-path">/api/v1/admin/login</div>
                            <div class="api-desc">管理员登录</div>
                        </div>
                        <div class="api-item">
                            <div class="api-method get">GET</div>
                            <div class="api-path">/api/v1/admin/users</div>
                            <div class="api-desc">获取用户列表</div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                .api-docs { max-width: 800px; margin: 0 auto; }
                .doc-section { margin-bottom: 2rem; }
                .doc-section h3 {
                    color: #333; margin-bottom: 1rem;
                    border-bottom: 2px solid #667eea; padding-bottom: 0.5rem;
                }
                .api-item {
                    display: flex; align-items: center; gap: 1rem;
                    padding: 1rem; margin-bottom: 0.5rem;
                    background: white; border-radius: 6px;
                    border: 1px solid #e1e5e9;
                }
                .api-method {
                    padding: 0.25rem 0.75rem; border-radius: 4px;
                    font-weight: 500; font-size: 0.875rem; min-width: 60px; text-align: center;
                }
                .api-method.get { background: #d4edda; color: #155724; }
                .api-method.post { background: #cce5ff; color: #004085; }
                .api-method.put { background: #fff3cd; color: #856404; }
                .api-method.delete { background: #f8d7da; color: #721c24; }
                .api-path {
                    font-family: monospace; font-weight: 500;
                    color: #333; min-width: 200px;
                }
                .api-desc { color: #666; flex: 1; }
            </style>
        `;

        this.updateContentArea(apiDocsContent);
    }
}

// 应用初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('应用开始初始化...');
    
    // 创建应用管理器实例
    window.appManager = new AppManager();
    
    // 绑定功能按钮事件
    const functionButtons = Utils.DOM.getAll('.function-btn');
    functionButtons.forEach(button => {
        button.addEventListener('click', () => {
            const functionName = button.dataset.function;
            if (functionName) {
                appManager.handleFunctionClick(functionName);
            }
        });
    });
    
    // 绑定模态框关闭事件
    const modalClose = Utils.DOM.get('#modalClose');
    if (modalClose) {
        modalClose.addEventListener('click', () => {
            Utils.Modal.hide();
        });
    }
    
    // 点击模态框外部关闭
    const modal = Utils.DOM.get('#modal');
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                Utils.Modal.hide();
            }
        });
    }
    
    // 初始化用户状态
    authManager.updateUserStatus();
    authManager.updatePermissions();
    
    // 显示欢迎页面
    appManager.showWelcome();
    
    console.log('应用初始化完成');
    Utils.Toast.success('系统加载完成');
});

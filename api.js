/**
 * API调用封装
 * 统一管理所有API接口调用
 */

class ApiService {
    constructor() {
        // API基础配置
        this.baseURL = 'http://localhost:8080/api/v1'; // 根据实际后端地址调整
        this.timeout = 10000; // 10秒超时
        this.token = null;
        
        // 从本地存储恢复token
        this.token = Utils.Storage.get('auth_token');
    }
    
    // 设置认证token
    setToken(token) {
        this.token = token;
        Utils.Storage.set('auth_token', token);
    }
    
    // 清除认证token
    clearToken() {
        this.token = null;
        Utils.Storage.remove('auth_token');
    }
    
    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        return headers;
    }
    
    // 基础请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            method: 'GET',
            headers: this.getHeaders(),
            timeout: this.timeout,
            ...options
        };
        
        try {
            console.log(`API请求: ${config.method} ${url}`, config.body ? JSON.parse(config.body) : '');
            
            const response = await fetch(url, config);
            const data = await response.json();
            
            const result = {
                success: response.ok,
                status: response.status,
                data: data.data || data,
                message: data.message || (response.ok ? '请求成功' : '请求失败'),
                code: data.code || response.status
            };
            
            console.log(`API响应: ${config.method} ${url}`, result);
            
            // 处理认证失败
            if (response.status === 401) {
                this.clearToken();
                Utils.Toast.error('登录已过期，请重新登录');
                // 可以在这里触发重新登录逻辑
            }
            
            return result;
        } catch (error) {
            console.error(`API错误: ${config.method} ${url}`, error);
            return {
                success: false,
                status: 0,
                data: null,
                message: error.message || '网络连接失败',
                code: 0
            };
        }
    }
    
    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseURL}${endpoint}`);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });
        
        return this.request(endpoint + url.search, { method: 'GET' });
    }
    
    // POST请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    // PUT请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    // DELETE请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
    
    // ========== 用户认证相关API ==========
    
    // 用户登录
    async userLogin(phone, password) {
        return this.post('/user/login', { phone, password });
    }
    
    // 用户注册
    async userRegister(phone, password, code, inviteCode) {
        return this.post('/user/register', {
            phone,
            password,
            code,
            invite_code: inviteCode
        });
    }
    
    // 发送注册验证码
    async sendRegisterCode(phone) {
        return this.post('/user/send-code', { phone });
    }
    
    // 获取用户信息
    async getUserInfo() {
        return this.get('/user/info');
    }
    
    // 修改密码
    async changePassword(oldPassword, newPassword) {
        return this.put('/user/change-password', {
            old_password: oldPassword,
            new_password: newPassword
        });
    }
    
    // ========== 管理员相关API ==========
    
    // 管理员登录
    async adminLogin(username, password) {
        return this.post('/admin/login', { username, password });
    }
    
    // 获取用户列表
    async getUserList(page = 1, limit = 10) {
        return this.get('/admin/users', { page, limit });
    }
    
    // 冻结/解冻用户
    async toggleUserStatus(userId, status) {
        return this.put(`/admin/users/${userId}/status`, { status });
    }
    
    // ========== 应用管理相关API ==========
    
    // 获取用户应用列表
    async getUserApps() {
        return this.get('/user/apps');
    }
    
    // 创建应用
    async createApp(name, type) {
        return this.post('/user/apps', { name, type });
    }
    
    // 获取应用详情
    async getAppDetail(appId) {
        return this.get(`/user/apps/${appId}`);
    }
    
    // 更新应用信息
    async updateApp(appId, data) {
        return this.put(`/user/apps/${appId}`, data);
    }
    
    // 重置应用密钥
    async resetAppSecret(appId) {
        return this.put(`/user/apps/${appId}/reset-secret`);
    }
    
    // 删除应用
    async deleteApp(appId) {
        return this.delete(`/user/apps/${appId}`);
    }
    
    // ========== 系统相关API ==========
    
    // 获取系统状态
    async getSystemStatus() {
        return this.get('/system/status');
    }
    
    // 获取API文档
    async getApiDocs() {
        return this.get('/system/docs');
    }
    
    // ========== 测试相关方法 ==========
    
    // 测试API连接
    async testConnection() {
        try {
            const result = await this.get('/system/ping');
            return result.success;
        } catch (error) {
            return false;
        }
    }
    
    // 模拟API响应（用于开发测试）
    async mockResponse(endpoint, data = {}, delay = 1000) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    status: 200,
                    data: data,
                    message: `模拟响应: ${endpoint}`,
                    code: 200
                });
            }, delay);
        });
    }
}

// 创建全局API服务实例
window.apiService = new ApiService();

// 导出API服务类
window.ApiService = ApiService;

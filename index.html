<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API系统管理界面</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <h1 class="logo">API系统管理界面</h1>
            <div class="user-status" id="userStatus">
                <span class="status-text">未登录</span>
                <button class="login-btn" id="loginBtn">登录</button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 功能面板 -->
        <div class="function-panel">
            <h2>功能测试面板</h2>
            <div class="function-grid">
                <!-- 用户相关功能 -->
                <div class="function-category">
                    <h3>用户功能</h3>
                    <div class="function-buttons">
                        <button class="function-btn" data-function="login">
                            <span class="btn-icon">🔑</span>
                            <span class="btn-text">用户登录</span>
                        </button>
                        <button class="function-btn" data-function="register">
                            <span class="btn-icon">📝</span>
                            <span class="btn-text">用户注册</span>
                        </button>
                        <button class="function-btn" data-function="change-password">
                            <span class="btn-icon">🔒</span>
                            <span class="btn-text">修改密码</span>
                        </button>
                        <button class="function-btn" data-function="reset-password">
                            <span class="btn-icon">🔄</span>
                            <span class="btn-text">重置密码</span>
                        </button>
                        <button class="function-btn" data-function="user-apps">
                            <span class="btn-icon">📱</span>
                            <span class="btn-text">应用管理</span>
                        </button>
                    </div>
                </div>

                <!-- 管理员功能 -->
                <div class="function-category">
                    <h3>管理员功能</h3>
                    <div class="function-buttons">
                        <button class="function-btn admin-only" data-function="admin-login">
                            <span class="btn-icon">👨‍💼</span>
                            <span class="btn-text">管理员登录</span>
                        </button>
                        <button class="function-btn admin-only" data-function="user-management">
                            <span class="btn-icon">👥</span>
                            <span class="btn-text">用户管理</span>
                        </button>
                        <button class="function-btn admin-only" data-function="system-settings">
                            <span class="btn-icon">⚙️</span>
                            <span class="btn-text">系统设置</span>
                        </button>
                    </div>
                </div>

                <!-- API测试功能 -->
                <div class="function-category">
                    <h3>API测试</h3>
                    <div class="function-buttons">
                        <button class="function-btn" data-function="api-test">
                            <span class="btn-icon">🔧</span>
                            <span class="btn-text">API测试工具</span>
                        </button>
                        <button class="function-btn" data-function="api-docs">
                            <span class="btn-icon">📚</span>
                            <span class="btn-text">API文档</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容显示区域 -->
        <div class="content-area" id="contentArea">
            <div class="welcome-message">
                <h2>欢迎使用API系统管理界面</h2>
                <p>请选择上方的功能按钮开始测试</p>
                <div class="system-info">
                    <h3>系统信息</h3>
                    <ul>
                        <li>当前版本: v1.0.0</li>
                        <li>构建时间: 2024-12-07</li>
                        <li>环境: 开发测试</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <span class="modal-close" id="modalClose">&times;</span>
            <div class="modal-body" id="modalBody">
                <!-- 模态框内容将动态加载 -->
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <span class="toast-message" id="toastMessage"></span>
    </div>

    <!-- JavaScript文件 -->
    <script src="utils.js"></script>
    <script src="api.js"></script>
    <script src="auth.js"></script>
    <script src="script.js"></script>
</body>
</html>

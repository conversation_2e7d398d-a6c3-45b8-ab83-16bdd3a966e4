/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 顶部导航栏 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
}

.user-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

.login-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* 主内容区域 */
.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* 功能面板 */
.function-panel {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.function-panel h2 {
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.function-grid {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.function-category h3 {
    margin-bottom: 1rem;
    color: #555;
    font-size: 1.1rem;
}

.function-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.function-btn {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
}

.function-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.function-btn.admin-only {
    border-color: #ffc107;
    background: #fff9e6;
}

.function-btn.admin-only:hover {
    border-color: #ff9800;
}

.function-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

.btn-icon {
    font-size: 1.5rem;
}

.btn-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* 内容显示区域 */
.content-area {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.welcome-message {
    text-align: center;
}

.welcome-message h2 {
    color: #333;
    margin-bottom: 1rem;
}

.welcome-message p {
    color: #666;
    margin-bottom: 2rem;
}

.system-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1.5rem;
    margin-top: 2rem;
    text-align: left;
}

.system-info h3 {
    margin-bottom: 1rem;
    color: #333;
}

.system-info ul {
    list-style: none;
}

.system-info li {
    padding: 0.25rem 0;
    color: #666;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    z-index: 1001;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 2rem;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #333;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1002;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #28a745;
}

.toast.error {
    background: #dc3545;
}

.toast.warning {
    background: #ffc107;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }
    
    .header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .function-buttons {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
